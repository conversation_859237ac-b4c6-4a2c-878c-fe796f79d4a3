import React, { useEffect, useState } from 'react';
import type { DatePickerProps } from 'antd';
import { DatePicker, message, Form, Select, Button, Card, Table } from 'antd';
import moment from 'moment';
import { queryGetTruckIndicator } from '@/services/ascAutoDigitalwin/api';

const { Option } = Select;


interface QueryParams {
  year: moment.Moment;
  month: string[];
  day: string[];
  team: string[];
  shift_number: string[];
  date_range?: any[];
}

// 生成1-12月的选项
const monthOptions = Array.from({ length: 12 }, (_, i) => ({
  label: `${i + 1}月`,
  value: `${i + 1}`,
}));

// 生成1-31日的选项
const dayOptions = Array.from({ length: 31 }, (_, i) => ({
  label: `${i + 1}日`,
  value: `${i + 1}`,
}));

const { RangePicker } = DatePicker;

const TruckIndicator: React.FC = () => {
  const [form] = Form.useForm();
  const [teamOptions, setTeamOptions] = useState<{ label: string; value: string }[]>([]);
  const [truckIndicatorData, setTruckIndicatorData] = useState<any>([])
  const [tableSummaryData, setTableSummaryData] = useState<any>()
  const [smallSummaryData, setSmallSummaryData] = useState<any>({})
  const [refresh, setRefresh] = useState(false);



  const onFinish = (values: QueryParams) => {
    const formattedValues = {
      ...values,
      year: values?.year?.format('YYYY')
    };
    fetchQueryGetTruckIndicator(formattedValues)
  };

  const fetchTeamOptions = async () => {
    try {
      const response = {
        data: ["1", "2", "3", "4"]
      };
      const options = response.data.map((team) => ({
        label: team,
        value: team
      }));
      setTeamOptions(options);
    } catch (error) {
      console.error('获取班组数据失败:', error);
    }
  };

  React.useEffect(() => {
    fetchTeamOptions();
  }, []);

  // 查询接口
  const fetchQueryGetTruckIndicator = async (params: any) => {
    const qryParams: any = {
      year: params?.year || '',
      month: params?.month || [],
      day: params?.day || [],
      team: params?.team || [],
      shift_number: params?.shift_number || [],
    }
    if (params?.date_range && params?.date_range.length === 2) {
      qryParams.start_time = params?.date_range[0]?.format('YYYYMMDD');
      qryParams.end_time = params?.date_range[1]?.format('YYYYMMDD');
      qryParams.if_interval = true;
    }
    const result = await queryGetTruckIndicator({ ...qryParams });
    if (result && result.success) {
      if ( !Object?.keys(result?.data)?.length || !Object?.keys(result?.data?.data)?.length) {
        setTruckIndicatorData([])
        setTableSummaryData({})
        setSmallSummaryData({})
        return message.info('查询数据为空！')
      } else {
        setTruckIndicatorData(result?.data?.data)
        setTableSummaryData(result?.data?.summary)
        setSmallSummaryData(result?.data?.small_summary || {})
      }
    }
  }
  // 导出CSV功能
  const exportToCSV = () => {

    if (!truckIndicatorData.length) {
      message.warning('暂无数据可导出');
      return;
    }
    const headers = [
      '日期\t\t\t',
      '班组\t\t\t',
      '工班号\t\t\t',
      '完成箱量(unit)\t\t\t',
      '完成TEU\t\t\t',
      '装船作业比例\t\t\t',
      '干线装船作业比例\t\t\t',
      '周转时间\t\t\t',
      '周转时间(实际)\t\t\t',
      '空驶时间\t\t\t',
      '取箱等待时间\t\t\t',
      '重车时间\t\t\t',
      '桥边重进重出率\t\t\t',
      '堆场重进重出率\t\t\t',
      '边装边卸比例\t\t\t',
      '装船人工干预率\t\t\t',
      '空驶距离(米)\t\t\t'
    ];
    let csvContent = headers.join(',') + '\n';
    truckIndicatorData.forEach((item: any) => {
      const row = [
        `${item?.year}-${item?.month}-${item?.day}` + '\t\t\t',
        item?.team + '\t\t\t',
        item?.shift_number + '\t\t\t',
        item.completed_cn_number?.toFixed(2) || '' + '\t\t\t',
        item.completed_teu_number?.toFixed(2) || '' + '\t\t\t',
        item.load_operation_proportion?.toFixed(2) || '' + '\t\t\t',
        item.ml_load_operation_proportion?.toFixed(2) || '' + '\t\t\t',
        item.cycle_time?.toFixed(2) || '' + '\t\t\t',
        item.actual_cycle_time?.toFixed(2) || '' + '\t\t\t',
        item.unload_move_time?.toFixed(2) || '' + '\t\t\t',
        item.pick_up_wait_time?.toFixed(2) || '' + '\t\t\t',
        item.loaded_time?.toFixed(2) || '' + '\t\t\t',
        item.qc_heavy_rate?.toFixed(2) || '' + '\t\t\t',
        item.yard_heavy_rate?.toFixed(2) || '' + '\t\t\t',
        item.load_and_unload_rate?.toFixed(2) || '' + '\t\t\t',
        item.load_manual_intervention_rate?.toFixed(2) || '' + '\t\t\t',
        item.unload_move_distance?.toFixed(2) || '' + '\t\t\t',
      ];
      csvContent += row.join(',') + '\n';
    });


    // 添加班组统计信息
    if (Object.keys(smallSummaryData).length > 0) {
      // csvContent += '\n班组统计\n';
      // csvContent += headers.join(',') + '\n';
      Object.keys(smallSummaryData).forEach(teamName => {
        const teamData = smallSummaryData[teamName];
        const teamRow = [
          '班组合计' + '\t\t\t',
          teamName + '\t\t\t',
          '\t\t\t',
          teamData.completed_cn_number?.toFixed(2) || '' + '\t\t\t',
          teamData.completed_teu_number?.toFixed(2) || '' + '\t\t\t',
          teamData.load_operation_proportion?.toFixed(2) || '' + '\t\t\t',
          teamData.ml_load_operation_proportion?.toFixed(2) || '' + '\t\t\t',
          teamData.cycle_time?.toFixed(2) || '' + '\t\t\t',
          teamData.actual_cycle_time?.toFixed(2) || '' + '\t\t\t',
          teamData.unload_move_time?.toFixed(2) || '' + '\t\t\t',
          teamData.pick_up_wait_time?.toFixed(2) || '' + '\t\t\t',
          teamData.loaded_time?.toFixed(2) || '' + '\t\t\t',
          teamData.qc_heavy_rate?.toFixed(2) || '' + '\t\t\t',
          teamData.yard_heavy_rate?.toFixed(2) || '' + '\t\t\t',
          teamData.load_and_unload_rate?.toFixed(2) || '' + '\t\t\t',
          teamData.load_manual_intervention_rate?.toFixed(2) || '' + '\t\t\t',
          teamData.unload_move_distance?.toFixed(2) || '' + '\t\t\t',
        ];
        csvContent += teamRow.join(',') + '\n';
      });
    }
    const footer = [
      '合计\t\t\t',
      '\t\t\t',
      '\t\t\t',
      tableSummaryData?.completed_cn_number + '\t\t\t',
      tableSummaryData?.completed_teu_number + '\t\t\t',
      tableSummaryData?.load_operation_proportion + '\t\t\t',
      tableSummaryData?.ml_load_operation_proportion + '\t\t\t',
      tableSummaryData?.cycle_time + '\t\t\t',
      tableSummaryData?.actual_cycle_time + '\t\t\t',
      tableSummaryData?.unload_move_time + '\t\t\t',
      tableSummaryData?.pick_up_wait_time + '\t\t\t',
      tableSummaryData?.loaded_time + '\t\t\t',
      tableSummaryData?.qc_heavy_rate + '\t\t\t',
      tableSummaryData?.yard_heavy_rate + '\t\t\t',
      tableSummaryData?.load_and_unload_rate + '\t\t\t',
      tableSummaryData?.load_manual_intervention_rate + '\t\t\t',
      tableSummaryData?.unload_move_distance + '\t\t\t'
    ];
    csvContent = csvContent + footer + '\n'
    // 创建Blob对象（使用UTF-16编码以更好地支持Excel）
    const blob = new Blob(['\ufeff' + csvContent], {
      type: 'text/csv;charset=utf-8;'
    });
    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    const fileName = `内集卡运行指标_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`;
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const columns = [
    {
      title: '日期',
      dataIndex: 'year',
      width: 110,
      key: 'year',
      render: (text: any, record: any, index: number) => {
        // 如果是合计行，合并前三列显示"合计"
        if (record.isTotalSummary) {
          return {
            children: '合计',
            props: {
              colSpan: 3, // 合并日期、班组、工班号三列
            },
          };
        }
        // 如果是班组统计行，日期列显示"班组合计"并合并四行
        if (record.isTeamSummary) {
          const teamData = getTeamSummaryData();
          const teamIndex = teamData.findIndex(item => item.team === record.team);
          if (teamIndex === 0) {
            // 只在第一行显示"班组合计"并设置rowSpan
            return {
              children: '班组合计',
              props: {
                rowSpan: teamData.length,
              },
            };
          } else {
            // 其他行不显示内容
            return {
              children: '',
              props: {
                rowSpan: 0,
              },
            };
          }
        }
        return `${record.year}-${record.month}-${record.day}`;
      }
    },
    {
      title: '班组',
      dataIndex: 'team',
      width: 80,
      key: 'team',
      render: (text: any, record: any) => {
        // 如果是合计行，不显示内容（已被日期列合并）
        if (record.isTotalSummary) {
          return {
            children: '',
            props: {
              colSpan: 0,
            },
          };
        }
        // 如果是班组统计行，班组列显示班组名并合并班组和工班号两列
        if (record.isTeamSummary) {
          return {
            children: text,
            props: {
              colSpan: 2, // 合并班组和工班号两列
            },
          };
        }
        return text;
      }
    },
    {
      title: '工班号',
      dataIndex: 'shift_number',
      width: 80,
      key: 'shift_number',
      render: (text: any, record: any) => {
        // 如果是合计行，不显示内容（已被日期列合并）
        if (record.isTotalSummary) {
          return {
            children: '',
            props: {
              colSpan: 0,
            },
          };
        }
        // 如果是班组统计行，不显示内容（已被班组列合并）
        if (record.isTeamSummary) {
          return {
            children: '',
            props: {
              colSpan: 0,
            },
          };
        }
        return text;
      }
    },
    {
      title: '完成箱量(unit)',
      dataIndex: 'completed_cn_number',
      width: 120,
      key: 'completed_cn_number',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '完成TEU',
      dataIndex: 'completed_teu_number',
      width: 100,
      key: 'completed_teu_number',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '装船作业比例',
      dataIndex: 'load_operation_proportion',
      width: 120,
      key: 'load_operation_proportion',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '干线装船作业比例',
      dataIndex: 'ml_load_operation_proportion',
      width: 140,
      key: 'ml_load_operation_proportion',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '周转时间',
      dataIndex: 'cycle_time',
      width: 100,
      key: 'cycle_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '周转时间(实际)',
      dataIndex: 'actual_cycle_time',
      width: 120,
      key: 'actual_cycle_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '空驶时间',
      dataIndex: 'unload_move_time',
      width: 100,
      key: 'unload_move_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '取箱等待时间',
      dataIndex: 'pick_up_wait_time',
      width: 120,
      key: 'pick_up_wait_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '重车时间',
      dataIndex: 'loaded_time',
      width: 100,
      key: 'loaded_time',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '桥边重进重出率',
      dataIndex: 'qc_heavy_rate',
      width: 130,
      key: 'qc_heavy_rate',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '堆场重进重出率',
      dataIndex: 'yard_heavy_rate',
      width: 130,
      key: 'yard_heavy_rate',
      render: (value: number) => value?.toFixed(2)
    },
    // {
    //     title: '桥边重进重出率(预空)',
    //     dataIndex: 'qc_heavy_rate_prefree',
    //     key: 'qc_heavy_rate_prefree',
    //     render: (value: number) => value?.toFixed(2)
    // },
    // {
    //     title: '堆场重进重出率(预空)',
    //     dataIndex: 'yard_heavy_rate_prefree',
    //     key: 'yard_heavy_rate_prefree',
    //     render: (value: number) => value?.toFixed(2)
    // },
    {
      title: '边装边卸比例',
      dataIndex: 'load_and_unload_rate',
      width: 120,
      key: 'load_and_unload_rate',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '装船人工干预率',
      dataIndex: 'load_manual_intervention_rate',
      width: 130,
      key: 'load_manual_intervention_rate',
      render: (value: number) => value?.toFixed(2)
    },
    {
      title: '空驶距离(米)',
      dataIndex: 'unload_move_distance',
      width: 120,
      key: 'unload_move_distance',
      render: (value: number) => value?.toFixed(2)
    },
  ];



  // 转换班组统计数据为表格数据格式
  const getTeamSummaryData = () => {
    return Object.keys(smallSummaryData).map(teamName => ({
      year: '',
      month: '',
      day: '',
      team: teamName,
      shift_number: '',
      ...smallSummaryData[teamName],
      isTeamSummary: true
    }));
  };





  const getStatus = (name: string) => {
    if (name !== 'date_range') {
      return form.getFieldValue('date_range');
    } else {
      const allData = form.getFieldsValue();
      return allData?.year || allData?.month || allData?.day || allData?.team || allData?.shift_number;
    }
  }

  return (
    <Card>
      <style>
        {`
          .team-summary-row {
            background-color: #f6ffed !important;
            font-weight: 500;
          }
          .total-summary-row {
            background-color: #fff2e8 !important;
            font-weight: bold;
          }
          .team-summary-row:hover {
            background-color: #f6ffed !important;
          }
          .total-summary-row:hover {
            background-color: #fff2e8 !important;
          }

        `}
      </style>
      <Form
        form={form}
        layout="inline"
        onFinish={onFinish}
        style={{ marginBottom: 24 }}
      >
        <Form.Item
          name="year"
          label="年份"
          rules={[
            {
              required: !form?.getFieldValue('date_range'),
              message: '请选择年份！',
            },
          ]}
        >
          <DatePicker
            picker="year"
            format="YYYY"
            onChange={() => setRefresh(!refresh)}
            disabled={getStatus('year')}
          />
        </Form.Item>

        <Form.Item name="month" label="月份">
          <Select
            mode="multiple"
            placeholder="请选择月份"
            style={{ width: 200 }}
            options={monthOptions}
            onChange={() => setRefresh(!refresh)}
            disabled={getStatus('month')}
          />
        </Form.Item>

        <Form.Item name="day" label="日期">
          <Select
            mode="multiple"
            placeholder="请选择日期"
            style={{ width: 200 }}
            options={dayOptions}
            onChange={() => setRefresh(!refresh)}
            disabled={getStatus('day')}
          />
        </Form.Item>

        <Form.Item name="team" label="班组">
          <Select
            mode="multiple"
            placeholder="请选择班组"
            style={{ width: 200 }}
            options={teamOptions}
            onChange={() => setRefresh(!refresh)}
            disabled={getStatus('team')}
          />
        </Form.Item>

        <Form.Item name="shift_number" label="工班号">
          <Select
            mode="multiple"
            placeholder="请选择工班号"
            style={{ width: 180 }}
            onChange={() => setRefresh(!refresh)}
            disabled={getStatus('shift_number')}
          >
            <Option value="1">1</Option>
            <Option value="2">2</Option>
          </Select>
        </Form.Item>

        <Form.Item name="date_range" label="时间区间">
          <RangePicker
            style={{ width: 220 }}
            disabled={getStatus('date_range')}
            onChange={() => setRefresh(!refresh)}
          />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit">
            查询
          </Button>
        </Form.Item>

        <Button
          type="primary"
          onClick={exportToCSV}
          disabled={!truckIndicatorData.length}
        >
          导出
        </Button>

        <Button
          type="default"
          onClick={() => {
            form?.resetFields();
            setRefresh(!refresh)
          }}
          style={{ marginLeft: 10 }}
        >
          重置
        </Button>
      </Form>

      <Table
        columns={columns}
        dataSource={truckIndicatorData}
        rowKey={(_, index) => `data-${index}`}
        pagination={false}
        scroll={{
          x: 'max-content',
          y: 400
        }}
        size="small"
        showHeader={true}
        tableLayout="fixed"
        style={{ marginTop: 24 }}
        summary={() => (
          <Table.Summary fixed>
            {/* 班组统计行 */}
            {getTeamSummaryData().map((teamData, index) => (
              <Table.Summary.Row key={`team-${teamData.team}`} className="team-summary-row">
                {/* 日期列 - 只在第一行显示"班组合计"并合并 */}
                {index === 0 ? (
                  <Table.Summary.Cell index={0} rowSpan={getTeamSummaryData().length}>
                    <div style={{ textAlign: 'center' }}>班组合计</div>
                  </Table.Summary.Cell>
                ) : null}
                {/* 班组列 - 显示班组名并合并工班号列 */}
                <Table.Summary.Cell index={1} colSpan={2}>
                  <div style={{ textAlign: 'center' }}>{teamData.team}</div>
                </Table.Summary.Cell>
                {/* 其他数据列 */}
                <Table.Summary.Cell index={3}>{teamData.completed_cn_number?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={4}>{teamData.completed_teu_number?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={5}>{teamData.load_operation_proportion?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={6}>{teamData.ml_load_operation_proportion?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={7}>{teamData.cycle_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={8}>{teamData.actual_cycle_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={9}>{teamData.unload_move_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={10}>{teamData.pick_up_wait_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={11}>{teamData.loaded_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={12}>{teamData.qc_heavy_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={13}>{teamData.yard_heavy_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={14}>{teamData.load_and_unload_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={15}>{teamData.load_manual_intervention_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={16}>{teamData.unload_move_distance?.toFixed(2)}</Table.Summary.Cell>
              </Table.Summary.Row>
            ))}

            {/* 总合计行 */}
            {tableSummaryData && (
              <Table.Summary.Row className="total-summary-row">
                <Table.Summary.Cell index={0} colSpan={3}>
                  <div style={{ textAlign: 'center' }}>合计</div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={3}>{tableSummaryData.completed_cn_number?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={4}>{tableSummaryData.completed_teu_number?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={5}>{tableSummaryData.load_operation_proportion?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={6}>{tableSummaryData.ml_load_operation_proportion?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={7}>{tableSummaryData.cycle_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={8}>{tableSummaryData.actual_cycle_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={9}>{tableSummaryData.unload_move_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={10}>{tableSummaryData.pick_up_wait_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={11}>{tableSummaryData.loaded_time?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={12}>{tableSummaryData.qc_heavy_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={13}>{tableSummaryData.yard_heavy_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={14}>{tableSummaryData.load_and_unload_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={15}>{tableSummaryData.load_manual_intervention_rate?.toFixed(2)}</Table.Summary.Cell>
                <Table.Summary.Cell index={16}>{tableSummaryData.unload_move_distance?.toFixed(2)}</Table.Summary.Cell>
              </Table.Summary.Row>
            )}
          </Table.Summary>
        )}
      />
    </Card>
  )
}

export default TruckIndicator;